# Processing Activity Register (GDPR Article 30)

This document explains the Processing Activity Register implementation for GDPR Article 30 compliance.

## Overview

The Processing Activity Register is a comprehensive system that documents all data processing activities as required by GDPR Article 30. It provides a complete record of:

- What personal data is processed
- Why it's processed (purposes and legal bases)
- Who has access to it (recipients)
- How long it's kept (retention periods)
- How it's protected (security measures)

## GDPR Article 30 Requirements

Under GDPR Article 30, controllers must maintain records of processing activities that include:

1. **Name and contact details** of the controller and DPO
2. **Purposes of processing**
3. **Categories of data subjects** and personal data
4. **Categories of recipients** (including third countries)
5. **International transfers** and safeguards
6. **Retention periods**
7. **Security measures** description

## System Architecture

### Database Schema

The system uses two main tables:

#### `processing_activities`
- Stores the main processing activity records
- JSONB columns for flexible data storage
- Full-text search capabilities
- Audit trail integration

#### `processing_activity_logs`
- Tracks all changes to processing activities
- Automatic logging via database triggers
- Complete audit trail for compliance

### Key Components

1. **`lib/processing-activity-register.ts`** - Core business logic
2. **`components/admin/processing-activity-register.tsx`** - Admin dashboard
3. **`app/api/admin/processing-activities/route.ts`** - REST API
4. **`scripts/initialize-processing-activities.ts`** - Setup script

## Standard Processing Activities

The system includes 5 pre-configured standard activities:

### 1. User Account Management
- **Purpose**: Account creation, authentication, service provision
- **Legal Basis**: Contract, Legitimate Interests
- **Data Categories**: Identification, Contact, Authentication data
- **Retention**: 3 years after account closure

### 2. Payment Processing
- **Purpose**: Payment processing, billing, fraud prevention
- **Legal Basis**: Contract, Legal Obligation
- **Data Categories**: Identification, Financial, Contact data
- **Retention**: 7 years for tax compliance
- **Third Country**: US (Creem.io) with Standard Contractual Clauses

### 3. Marketing Communications
- **Purpose**: Direct marketing, product updates, engagement
- **Legal Basis**: Consent, Legitimate Interests
- **Data Categories**: Identification, Contact, Marketing, Behavioral data
- **Retention**: 2 years after consent withdrawal

### 4. Website Analytics
- **Purpose**: Website optimization, UX improvement, monitoring
- **Legal Basis**: Consent, Legitimate Interests
- **Data Categories**: Technical, Usage, Behavioral data
- **Retention**: 2 years with anonymization

### 5. Customer Support
- **Purpose**: Customer service, issue resolution, improvement
- **Legal Basis**: Contract, Legitimate Interests
- **Data Categories**: Identification, Contact, Usage, Technical data
- **Retention**: 1 year after case closure with anonymization

## Setup and Installation

### 1. Database Migration

Run the migration to create the required tables:

```bash
# Apply the migration
supabase migration up

# Or manually run the SQL file
psql -f supabase/migrations/20241201000000_processing_activities.sql
```

### 2. Initialize Standard Activities

```bash
# Using npm script
bun run processing-activities:init

# Or directly
bun run scripts/initialize-processing-activities.ts

# Get help
bun run processing-activities:help
```

### 3. Access the Dashboard

Navigate to `/admin/processing-activities` to access the management interface.

## API Usage

### Get All Processing Activities

```bash
GET /api/admin/processing-activities

# With filters
GET /api/admin/processing-activities?category=identification_data&legal_basis=consent

# Summary format
GET /api/admin/processing-activities?format=summary
```

### Create New Processing Activity

```bash
POST /api/admin/processing-activities
Content-Type: application/json

{
  "activity_name": "Customer Feedback Collection",
  "controller_name": "GenAForm SaaS Platform",
  "controller_contact": "<EMAIL>",
  "purposes": ["Service improvement", "Customer satisfaction"],
  "legal_bases": ["legitimate_interests"],
  "data_subject_categories": ["customers"],
  "personal_data_categories": ["identification_data", "usage_data"],
  "retention_periods": [{
    "data_category": "usage_data",
    "retention_period": "2 years",
    "retention_days": 730,
    "legal_basis": "Service improvement",
    "deletion_method": "anonymize"
  }],
  "security_measures": ["Data encryption", "Access controls"]
}
```

### Initialize Standard Activities

```bash
POST /api/admin/processing-activities
Content-Type: application/json

{
  "initialize_standard": true
}
```

## Dashboard Features

### Overview Dashboard
- Total activities count
- Legal basis breakdown
- Data category statistics
- Last update timestamps

### Activity Management
- View all processing activities
- Detailed activity inspection
- Export functionality
- Search and filtering

### Detailed Activity View
- Complete GDPR Article 30 information
- Legal basis visualization
- Retention period details
- Security measures documentation
- International transfer information

## Export and Reporting

### JSON Export
The dashboard provides export functionality that generates a complete JSON file containing:

```json
{
  "generated_at": "2024-12-01T10:00:00Z",
  "controller": "GenAForm SaaS Platform",
  "contact": "<EMAIL>",
  "activities": [...],
  "summary": {...}
}
```

### Compliance Reports
The system generates reports suitable for:
- Data Protection Authority inspections
- Internal compliance audits
- Third-party assessments
- Legal documentation

## Security and Access Control

### Row Level Security (RLS)
- Admin users can view all activities
- Users can view activities they created
- Service role has full access
- Automatic audit logging

### Data Protection
- All changes are logged
- IP address and user agent tracking
- Automatic timestamps
- Immutable audit trail

## Maintenance

### Regular Tasks

1. **Review Activities** - Quarterly review of all processing activities
2. **Update Retention Periods** - Annual review of retention policies
3. **Security Measures** - Regular update of security documentation
4. **Legal Basis Review** - Annual review of legal bases

### Monitoring

Monitor the register through:

```sql
-- Check recent activity
SELECT * FROM processing_activity_logs 
WHERE created_at > NOW() - INTERVAL '30 days'
ORDER BY created_at DESC;

-- Get activity statistics
SELECT 
  COUNT(*) as total_activities,
  COUNT(*) FILTER (WHERE system_generated = true) as system_generated,
  COUNT(*) FILTER (WHERE system_generated = false) as user_created
FROM processing_activities;
```

## Compliance Checklist

- [ ] All processing activities documented
- [ ] Legal bases clearly identified
- [ ] Retention periods specified
- [ ] Security measures described
- [ ] International transfers documented
- [ ] Regular reviews scheduled
- [ ] Staff training completed
- [ ] Export procedures tested

## Troubleshooting

### Common Issues

1. **Migration Fails**
   - Check database permissions
   - Verify Supabase connection
   - Review migration logs

2. **Standard Activities Not Created**
   - Run initialization script
   - Check for existing activities
   - Verify API permissions

3. **Dashboard Access Denied**
   - Verify admin permissions
   - Check authentication status
   - Review RLS policies

### Recovery Procedures

If data is lost or corrupted:

1. Check audit logs for recent changes
2. Restore from database backup
3. Re-run initialization script
4. Verify all activities are present

## Legal Considerations

### GDPR Compliance
This system helps ensure compliance with:
- Article 30 (Records of processing activities)
- Article 5 (Principles of processing)
- Article 6 (Lawfulness of processing)
- Article 13-14 (Information to data subjects)

### Documentation Requirements
Maintain documentation for:
- Legal basis assessments
- Legitimate interests assessments
- Data protection impact assessments
- International transfer agreements

### Regular Reviews
Schedule regular reviews to ensure:
- Activities remain current
- Legal bases are still valid
- Retention periods are appropriate
- Security measures are adequate

## Support

For technical support or compliance questions:
- Email: <EMAIL>
- Documentation: `/docs/processing-activity-register.md`
- API Reference: `/api/admin/processing-activities`
