export interface ConfigProps {
  appName: string;
  appDescription: string;
  domainName: string;

  // Legacy Stripe configuration (not used - Creem is the payment provider)
  stripe: {
    plans: {
      isFeatured?: boolean;
      priceId: string;
      name: string;
      description?: string;
      price: number;
      priceAnchor?: number;
      features: {
        name: string;
      }[];
    }[];
  };

  email: {
    subdomain: string;
    fromNoReply: string;
    fromAdmin: string;
    supportEmail?: string;
    forwardRepliesTo?: string;
  };
}
